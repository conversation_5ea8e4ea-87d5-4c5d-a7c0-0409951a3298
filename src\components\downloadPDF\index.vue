<template>
  <div class="downloadPDF"></div>
  <view :variation :change:variation="renderScript.watchVariationChange" />
</template>

<script>
export default {
  emits: ['markerClick', 'mapClick', 'mapLoad'],
  props: { variation: { type: Object, default: null } },
  data() {
    return {}
  },
  methods: {
    downPdf(path) {
      var fileName = new Date().valueOf() + '.pdf'
      let that = this
      this.base64ToFile(path, fileName, function (path1) {
        uni.showToast({
          title: `已保存在文件夹下！位置为：/storage/emulated/0/PDF存放处/${fileName}`,
          icon: 'none',
          duration: 2000,
          position: 'top'
        })
        setTimeout(() => {
          //自行控制是否打开文件

          //用第三方程序打开文件
          plus.runtime.openFile(`/storage/emulated/0/PDF存放处/${fileName}`, {}, function (error) {
            plus.nativeUI.toast(error.message)
          })
        }, 2000)
      })
    },
    base64ToFile(base64Str, fileName, callback) {
      //申请本地存储读写权限，创建文件夹
      plus.android.requestPermissions(
        ['android.permission.WRITE_EXTERNAL_STORAGE', 'android.permission.READ_EXTERNAL_STORAGE', 'android.permission.INTERNET', 'android.permission.ACCESS_WIFI_STATE'],
        (error) => {
          const File = plus.android.importClass('java.io.File')
          let file = new File('/storage/emulated/0/PDF存放处')
          if (!file.exists()) {
            //文件夹不存在即创建
            return file.mkdirs()
          }
          return false
        },
        (success) => {
          uni.showToast({
            title: '无法获取权限，文件下载将出错！',
            icon: 'none'
          })
        }
      )
      // 去除base64前缀,进行文件保存
      var index = base64Str.indexOf(',')
      var base64Str = base64Str.slice(index + 1, base64Str.length)
      let that = this
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function (fs) {
        fs.root.getFile(
          fileName,
          {
            create: true
          },
          function (entry) {
            // 获得本地路径URL，file:///xxx/doc/1663062980631.xlsx
            var fullPath = '/storage/emulated/0/PDF存放处/' + fileName
            var Base64 = plus.android.importClass('android.util.Base64')
            var FileOutputStream = plus.android.importClass('java.io.FileOutputStream')
            var out = new FileOutputStream(fullPath)
            // 此处Base64.decode有长度限制，如果不能满足需求，可以考虑换成官方原生插件市场的【Base64转文件】
            var bytes = Base64.decode(base64Str, Base64.DEFAULT)
            //转成Array避免长度限制
            // let bytes = that.base64ToByteArray(base64Str)
            out.write(bytes)
            out.close()
            // 回调
            callback && callback()
          }
        )
      })
    },
    showLoading() {
      uni.showLoading({ title: '下载中...' })
    },
    hideLoading() {
      uni.hideLoading()
    }
  }
}
</script>

<script lang="renderjs" module="renderScript">
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
export default {
  methods: {
    watchVariationChange({type, value}) {
      if(type && value) this[type](value)
    },
    // 页面导出pdf
    download(id){
			this.$ownerInstance.callMethod('showLoading'); //显示加载
      const detail = document.querySelector(id);
			html2Canvas(detail, {
				allowTaint: true,
				useCORS: true,
				scale: 2, // 提高分辨率
				windowHeight: detail.scrollHeight // 确保捕获完整高度
			}).then((canvas) => {
				return new Promise((resolve) => {
					setTimeout(() => resolve(canvas), 500)
				}).then((canvas) => {
					const contentWidth = canvas.width;
					const contentHeight = canvas.height;

					// 创建自定义尺寸的PDF（核心修改）
					const pdf = new JsPDF({
						orientation: contentWidth > contentHeight ? 'l' : 'p', // 自动方向
						unit: 'px',
						format: [contentWidth, contentHeight] // 完全匹配内容尺寸
					});

					// 直接添加完整内容（移除分页逻辑）
					pdf.addImage(canvas, 'PNG', 0, 0, contentWidth, contentHeight);

					const blob = pdf.output("datauristring");
					this.$ownerInstance.callMethod('downPdf', blob);
				}).catch((r) => {
					console.log(r);
					this.$ownerInstance.callMethod('hideLoading');
				})
			});
    }
  },
}
</script>

<style lang="less" scoped>
.downloadPDF {
}
</style>
