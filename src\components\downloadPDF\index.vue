<template>
  <div class="downloadPDF"></div>
  <view :variation :change:variation="renderScript.watchVariationChange" />
</template>

<script>
export default {
  emits: ['markerClick', 'mapClick', 'mapLoad'],
  props: { variation: { type: Object, default: null } },
  data() {
    return {}
  },
  methods: {
    downPdf(path) {
      var fileName = new Date().valueOf() + '.pdf'
      let that = this
      this.base64ToFile(path, fileName, function (path1) {
        uni.showToast({
          title: `已保存在文件夹下！位置为：/storage/emulated/0/PDF存放处/${fileName}`,
          icon: 'none',
          duration: 2000,
          position: 'top'
        })
        setTimeout(() => {
          //自行控制是否打开文件

          //用第三方程序打开文件
          plus.runtime.openFile(`/storage/emulated/0/PDF存放处/${fileName}`, {}, function (error) {
            plus.nativeUI.toast(error.message)
          })
        }, 2000)
      })
    },
    base64ToFile(base64Str, fileName, callback) {
      console.log('开始处理PDF文件:', fileName)
      console.log('Base64字符串长度:', base64Str.length)

      //申请本地存储读写权限，创建文件夹
      plus.android.requestPermissions(
        ['android.permission.WRITE_EXTERNAL_STORAGE', 'android.permission.READ_EXTERNAL_STORAGE', 'android.permission.INTERNET', 'android.permission.ACCESS_WIFI_STATE'],
        (error) => {
          const File = plus.android.importClass('java.io.File')
          let file = new File('/storage/emulated/0/PDF存放处')
          if (!file.exists()) {
            //文件夹不存在即创建
            console.log('创建PDF存放目录')
            return file.mkdirs()
          }
          return false
        },
        (success) => {
          uni.showToast({
            title: '无法获取权限，文件下载将出错！',
            icon: 'none'
          })
          console.error('权限获取失败')
        }
      )

      // 去除base64前缀,进行文件保存
      var index = base64Str.indexOf(',')
      if (index !== -1) {
        base64Str = base64Str.slice(index + 1, base64Str.length)
        console.log('去除前缀后Base64长度:', base64Str.length)
      } else {
        console.log('未找到Base64前缀，直接使用原字符串')
      }

      let that = this
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function (fs) {
        fs.root.getFile(
          fileName,
          {
            create: true
          },
          function (entry) {
            try {
              // 获得本地路径URL，file:///xxx/doc/1663062980631.xlsx
              var fullPath = '/storage/emulated/0/PDF存放处/' + fileName
              console.log('准备写入文件到:', fullPath)

              var Base64 = plus.android.importClass('android.util.Base64')
              var FileOutputStream = plus.android.importClass('java.io.FileOutputStream')
              var out = new FileOutputStream(fullPath)

              // 使用自定义方法避免Base64.decode的长度限制
              let bytes
              try {
                // 尝试使用原生Base64解码（适用于较小文件）
                console.log('尝试使用原生Base64解码')
                bytes = Base64.decode(base64Str, Base64.DEFAULT)
                console.log('原生Base64解码成功，字节数组长度:', bytes.length)
              } catch (error) {
                console.log('Base64.decode失败，使用自定义方法:', error.message)
                // 如果原生方法失败，使用自定义方法处理长字符串
                bytes = that.base64ToByteArray(base64Str)
                console.log('自定义方法解码成功，字节数组长度:', bytes ? bytes.length : 'null')
              }

              if (!bytes || bytes.length === 0) {
                throw new Error('字节数组为空，无法写入文件')
              }

              console.log('开始写入文件，字节数:', bytes.length)
              out.write(bytes)
              out.close()
              console.log('文件写入完成:', fullPath)

              // 验证文件是否成功创建
              const File = plus.android.importClass('java.io.File')
              const savedFile = new File(fullPath)
              if (savedFile.exists()) {
                console.log('文件验证成功，文件大小:', savedFile.length(), '字节')
                // 回调
                callback && callback()
              } else {
                throw new Error('文件创建失败')
              }
            } catch (fileError) {
              console.error('文件写入过程出错:', fileError)
              that.hideLoading()
              uni.showToast({
                title: '文件保存失败: ' + fileError.message,
                icon: 'none',
                duration: 3000
              })
            }
          }
        )
      })
    },
    // 自定义base64转字节数组方法，避免长度限制
    base64ToByteArray(base64Str) {
      try {
        console.log('开始JavaScript atob解码，字符串长度:', base64Str.length)

        // 使用JavaScript的atob进行解码
        const binaryString = atob(base64Str)
        const len = binaryString.length
        console.log('atob解码成功，二进制字符串长度:', len)

        // 直接创建Java字节数组
        const javaBytes = plus.android.newObject('byte[]', len)

        for (let i = 0; i < len; i++) {
          const charCode = binaryString.charCodeAt(i)
          // 处理有符号字节（Java字节范围是-128到127）
          javaBytes[i] = charCode > 127 ? charCode - 256 : charCode
        }

        console.log('Java字节数组创建成功，长度:', len)
        return javaBytes
      } catch (error) {
        console.error('base64ToByteArray转换失败:', error.message)
        // 如果JavaScript方法也失败，尝试分块处理
        return this.base64ToByteArrayChunked(base64Str)
      }
    },
    // 分块处理超长base64字符串
    base64ToByteArrayChunked(base64Str) {
      const chunkSize = 8192 // 8KB chunks
      const chunks = []

      // 分块解码
      for (let i = 0; i < base64Str.length; i += chunkSize) {
        const chunk = base64Str.slice(i, i + chunkSize)
        try {
          const Base64 = plus.android.importClass('android.util.Base64')
          const chunkBytes = Base64.decode(chunk, Base64.DEFAULT)
          chunks.push(chunkBytes)
        } catch (error) {
          console.error(`分块${i}解码失败:`, error)
          // 如果分块也失败，使用JavaScript方法
          const binaryString = atob(chunk)
          const chunkArray = new Array(binaryString.length)
          for (let j = 0; j < binaryString.length; j++) {
            chunkArray[j] = binaryString.charCodeAt(j)
          }
          chunks.push(chunkArray)
        }
      }

      // 合并所有分块
      let totalLength = 0
      chunks.forEach((chunk) => {
        totalLength += chunk.length
      })

      console.log('分块处理完成，总长度:', totalLength)

      // 创建Java字节数组
      const result = plus.android.newObject('byte[]', totalLength)

      let offset = 0
      chunks.forEach((chunk) => {
        for (let i = 0; i < chunk.length; i++) {
          const value = chunk[i]
          // 处理有符号字节
          result[offset + i] = value > 127 ? value - 256 : value
        }
        offset += chunk.length
      })

      console.log('分块合并完成，最终字节数组长度:', totalLength)
      return result
    },
    showLoading() {
      uni.showLoading({ title: '下载中...' })
    },
    hideLoading() {
      uni.hideLoading()
    }
  }
}
</script>

<script lang="renderjs" module="renderScript">
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
export default {
  methods: {
    watchVariationChange({type, value}) {
      if(type && value) this[type](value)
    },
    // 页面导出pdf
    download(id){
			this.$ownerInstance.callMethod('showLoading'); //显示加载
      const detail = document.querySelector(id);
			html2Canvas(detail, {
				allowTaint: true,
				useCORS: true,
				scale: 2, // 提高分辨率
				windowHeight: detail.scrollHeight // 确保捕获完整高度
			}).then((canvas) => {
				return new Promise((resolve) => {
					setTimeout(() => resolve(canvas), 500)
				}).then((canvas) => {
					const contentWidth = canvas.width;
					const contentHeight = canvas.height;

					// 创建自定义尺寸的PDF（核心修改）
					const pdf = new JsPDF({
						orientation: contentWidth > contentHeight ? 'l' : 'p', // 自动方向
						unit: 'px',
						format: [contentWidth, contentHeight] // 完全匹配内容尺寸
					});

					// 直接添加完整内容（移除分页逻辑）
					pdf.addImage(canvas, 'PNG', 0, 0, contentWidth, contentHeight);

					const blob = pdf.output("datauristring");
					this.$ownerInstance.callMethod('downPdf', blob);
				}).catch((r) => {
					console.log(r);
					this.$ownerInstance.callMethod('hideLoading');
				})
			});
    }
  },
}
</script>

<style lang="less" scoped>
.downloadPDF {
}
</style>
