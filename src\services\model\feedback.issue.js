import { nodeRequest } from '../index'

// 创建反馈
export const feedbackIssueApi = (data) => {
  return nodeRequest.post('/nodeServer/feedback', data)
}
export const feedbackIssueCreateRecordApi = (data) => {
  return nodeRequest.post('/nodeServer/feedback/createRecord', data)
}

// 获取所有反馈问题
export const feedbackIssueListApi = () => {
  return nodeRequest.get(`/nodeServer/feedback/list`)
}
// 获取单个反馈详情
export const feedbackIssueDetailApi = (id) => {
  return nodeRequest.get(`/nodeServer/feedback/${id}`)
}

// 反馈回复列表
export const feedbackIssueReplyListApi = (id) => {
  return nodeRequest.get(`/nodeServer/feedback/reply/${id}`)
}

// 创建回复
export const feedbackIssueReplyApi = (data) => {
  return nodeRequest.post(`/nodeServer/feedback/reply`, data)
}

// 更新回复
export const feedbackIssueReplyUpdateApi = (data) => {
  return nodeRequest.put(`/nodeServer/feedback/reply`, data)
}

// 已阅读
export const feedbackReplyReadApi = (data) => {
  return nodeRequest.post(`/nodeServer/feedback/read`, data)
}

// 待处理回复数量
export const feedbackPendingCountApi = () => {
  return nodeRequest.get(`/nodeServer/feedback/reply/pending/count`)
}

//待回复列表
export const feedbackPendingListApi = () => {
  return nodeRequest.get(`/nodeServer/feedback/listRecord`)
}
