<template>
  <div class="feedback-record-page all flex f-column">
    <!-- 页面头部 -->
    <div class="page-header pad-24 back-white">
      <div class="header-content f-between f-y-center">
        <div class="header-title">
          <div class="title-text fon-S36 fon-W700 color-333">反馈记录</div>
          <div class="subtitle-text fon-S24 color-666 mar-T4">待处理反馈列表</div>
        </div>
        <div class="stats-badge f-xy-center">
          <div class="stats-number fon-S28 fon-W600 color-white">{{ pendingList.length }}</div>
        </div>
      </div>
    </div>

    <!-- 列表内容 -->
    <div class="f-1 overflow-auto pad-16 back-f5f5f5">
      <!-- 空状态 -->
      <div v-if="pendingList.length === 0" class="empty-state f-xy-center f-column">
        <div class="empty-icon f-xy-center mar-B24">
          <wd-icon name="chat" size="48px" color="#ccc"></wd-icon>
        </div>
        <div class="empty-text fon-S28 color-999">暂无待处理反馈</div>
        <div class="empty-desc fon-S24 color-ccc mar-T8">所有反馈都已处理完成</div>
      </div>

      <!-- 反馈卡片列表 -->
      <template v-for="(item, index) in pendingList" :key="item.FeedbackID">
        <div @click="handleItemClick(item)" class="feedback-card mar-B24" :class="[item.pendingCount > 0 ? 'card-urgent' : 'card-completed', { 'card-unread': !item.IsReadByCurrentUser }]" :style="{ animationDelay: index * 0.1 + 's' }">
          <!-- 未读角标 -->
          <div v-if="item.IsReadByCurrentUser === 0 && item.newestReply" class="unread-indicator">
            <div class="indicator-pulse"></div>
          </div>

          <!-- 状态条 -->
          <div class="status-stripe" :class="item.pendingCount > 0 ? 'stripe-urgent' : 'stripe-completed'"></div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <!-- 头部区域 -->
            <div class="card-top f-between f-y-center mar-B20">
              <div class="feedback-header f-y-center f-1">
                <div class="avatar-wrapper mar-R16">
                  <div class="user-avatar f-xy-center" :class="item.pendingCount > 0 ? 'avatar-urgent' : 'avatar-completed'">
                    <wd-icon name="user-o" size="20px" color="#fff"></wd-icon>
                  </div>
                  <div class="status-dot" :class="item.pendingCount > 0 ? 'dot-urgent' : 'dot-completed'"></div>
                </div>
                <div class="header-info f-1">
                  <div class="feedback-title fon-S32 fon-W600 color-333 mar-B4">{{ item.FeedbackContent }}</div>
                  <div class="feedback-meta f-y-center">
                    <span class="user-name fon-S24 color-666 mar-R16">{{ item.UserName || '匿名用户' }}</span>
                    <span class="time-info fon-S22 color-999">{{ formatTime(item?.newestReply?.ModifyTime) }}</span>
                  </div>
                </div>
              </div>
              <div class="status-badge" :class="item.pendingCount > 0 ? 'badge-urgent' : 'badge-completed'">
                <div class="badge-number fon-S28 fon-W700">{{ item.pendingCount }}</div>
                <div class="badge-label fon-S20">{{ item.pendingCount > 0 ? '待处理' : '已完成' }}</div>
              </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-section mar-B20" v-if="item?.newestReply?.ReplyContent">
              <div class="content-box">
                <div class="content-text fon-S28 color-666 text-nowrap-3">{{ item?.newestReply?.ReplyContent }}</div>
              </div>
            </div>

            <!-- 底部信息 -->
            <div class="card-bottom f-between f-y-center">
              <div class="file-section f-y-center">
                <div class="file-badge f-y-center" @click.stop="handleToDetail(item)">
                  <div class="view-hint fon-S22 color-666 mar-R12">查看档案</div>
                  <span class="file-text fon-S24 color-999">{{ item.FileCode || '无档案编码' }}</span>
                </div>
              </div>
              <div class="action-section f-y-center">
                <div class="view-hint fon-S22 color-ccc mar-R12">查看详情</div>
                <div class="arrow-wrapper f-xy-center">
                  <wd-icon name="arrow-right" size="16px" color="#d9d9d9"></wd-icon>
                </div>
              </div>
            </div>
          </div>

          <!-- 悬浮效果层 -->
          <div class="hover-overlay"></div>
        </div>
      </template>
    </div>
    <FeedbackPopup ref="feedbackRef" @close="onClosePopup"></FeedbackPopup>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onBackPress } from '@dcloudio/uni-app'

import { feedbackPendingListApi } from '/src/services/model/feedback.issue.js'
import FeedbackPopup from '../material/components/FeedbackPopup/index.vue'

const feedbackRef = ref(null)
const pendingList = ref([])

onBackPress(() => {
  const isShow = feedbackRef.value.getFeedbackIsOpen()
  if (isShow) {
    feedbackRef.value.close()
    return true
  }
})

function onClosePopup() {
  getFeedbackPendingList()
}

// 获取反馈列表
async function getFeedbackPendingList() {
  try {
    const { data } = await feedbackPendingListApi()
    pendingList.value =
      data.map((item) => {
        item.newestReply = item.newestReply ? JSON.parse(item.newestReply) : null
        item.createUserInfo = item.createUserInfo ? JSON.parse(item.createUserInfo) : {}
        return item
      }) || []
  } catch (error) {
    console.error('获取反馈列表失败:', error)
    pendingList.value = []
  }
}

// 格式化时间
function formatTime(timeStr) {
  if (!timeStr) return '--'
  try {
    const date = new Date(timeStr)
    const now = new Date()
    const diff = now - date
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return '今天'
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  } catch (error) {
    return timeStr.slice(0, 10) || '--'
  }
}

// 处理卡片点击
function handleItemClick(item) {
  feedbackRef.value.getFeedbackDetail(item.FeedbackID)
}

// 初始化数据
getFeedbackPendingList()

function handleToDetail({ FileCode }) {
  uni.navigateTo({ url: `/src/pages/mine/children/material/detail?xqbm=${FileCode}` })
}
</script>

<style lang="less" scoped>
.feedback-record-page {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100%;
}

/* 页面头部样式 */
.page-header {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: linear-gradient(90deg, transparent 0%, rgba(24, 144, 255, 0.2) 50%, transparent 100%);
  }
}

.header-content {
  position: relative;
  z-index: 2;
}

.title-text {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-badge {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border-radius: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -2rpx;
    left: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    background: linear-gradient(135deg, #ff4d4f, #ff7875);
    border-radius: 30rpx;
    z-index: -1;
    opacity: 0.3;
    animation: pulse 2s infinite;
  }
}

/* 空状态样式 */
.empty-state {
  padding: 120rpx 40rpx;

  .empty-icon {
    width: 120rpx;
    height: 120rpx;
    background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
    border-radius: 60rpx;
  }
}

/* 反馈卡片样式 */
.feedback-card {
  position: relative;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  animation: slideInUp 0.6s ease-out both;
  border: 1rpx solid #f0f0f0;

  &:active {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  }

  // 紧急状态卡片
  &.card-urgent {
    border-left: 6rpx solid #ff4757;

    .status-stripe.stripe-urgent {
      background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
    }
  }

  // 已完成状态卡片
  &.card-completed {
    border-left: 6rpx solid #2ed573;

    .status-stripe.stripe-completed {
      background: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
    }
  }

  // 未读状态
  &.card-unread {
    border: 1rpx solid rgba(64, 169, 255, 0.2);
    box-shadow: 0 4rpx 25rpx rgba(64, 169, 255, 0.1);

    .unread-indicator {
      opacity: 1;
    }
  }
}

/* 未读指示器 */
.unread-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  z-index: 10;
  opacity: 0;

  .indicator-pulse {
    width: 12rpx;
    height: 12rpx;
    background: #ff4757;
    border-radius: 50%;
    animation: pulseGlow 2s infinite;
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
  }
}

/* 状态条 */
.status-stripe {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  z-index: 1;
}

/* 卡片内容 */
.card-content {
  padding: 24rpx 28rpx 28rpx;
  position: relative;
  z-index: 2;
}

/* 头像区域 */
.avatar-wrapper {
  position: relative;

  .user-avatar {
    width: 56rpx;
    height: 56rpx;
    border-radius: 50%;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &.avatar-urgent {
      background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
    }

    &.avatar-completed {
      background: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
    }
  }

  .status-dot {
    position: absolute;
    bottom: -2rpx;
    right: -2rpx;
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    border: 3rpx solid #ffffff;

    &.dot-urgent {
      background: #ff4757;
    }

    &.dot-completed {
      background: #2ed573;
    }
  }
}

/* 状态徽章 */
.status-badge {
  text-align: center;
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  min-width: 80rpx;

  &.badge-urgent {
    background: linear-gradient(135deg, rgba(255, 71, 87, 0.1) 0%, rgba(255, 107, 122, 0.05) 100%);
    border: 1rpx solid rgba(255, 71, 87, 0.2);

    .badge-number {
      color: #ff4757;
    }

    .badge-label {
      color: #ff6b7a;
    }
  }

  &.badge-completed {
    background: linear-gradient(135deg, rgba(46, 213, 115, 0.1) 0%, rgba(123, 237, 159, 0.05) 100%);
    border: 1rpx solid rgba(46, 213, 115, 0.2);

    .badge-number {
      color: #2ed573;
    }

    .badge-label {
      color: #7bed9f;
    }
  }
}

/* 内容区域 */
.content-section {
  .content-box {
    background: rgba(248, 250, 252, 0.6);
    border-radius: 12rpx;
    padding: 20rpx 24rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.02);

    .content-text {
      line-height: 1.6;
      word-break: break-word;
      color: #5a6c7d;
    }
  }
}

/* 底部区域 */
.card-bottom {
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.04);
}

/* 文件徽章 */
.file-badge {
  background: rgba(162, 236, 65, 0.4);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;

  .file-text {
    color: #159ed4;
  }
}

/* 操作区域 */
.action-section {
  .view-hint {
    color: #bfbfbf;
  }

  .arrow-wrapper {
    width: 32rpx;
    height: 32rpx;
    background: rgba(217, 217, 217, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
  }
}

/* 悬浮效果层 */
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 169, 255, 0.02) 0%, rgba(64, 169, 255, 0.01) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 20rpx;
}

.feedback-card:active .hover-overlay {
  opacity: 1;
}

.feedback-card:active .arrow-wrapper {
  background: rgba(64, 169, 255, 0.1);
  transform: translateX(4rpx);
}

/* 动画效果 */
@keyframes pulseGlow {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10rpx rgba(255, 71, 87, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
}

@keyframes statusPulse {
  0%,
  100% {
    box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.2);
  }
  50% {
    box-shadow: 0 0 0 8rpx rgba(82, 196, 26, 0.1);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 20rpx 16rpx;
  }

  .feedback-card {
    margin-bottom: 20rpx;
    border-radius: 16rpx;
  }

  .card-content {
    padding: 20rpx 24rpx 24rpx;
  }

  .card-top {
    margin-bottom: 16rpx;
  }

  .user-avatar {
    width: 48rpx;
    height: 48rpx;
  }

  .status-dot {
    width: 16rpx;
    height: 16rpx;
    border-width: 2rpx;
  }

  .status-badge {
    padding: 10rpx 14rpx;
    min-width: 70rpx;
  }

  .content-section {
    margin-bottom: 16rpx;
  }

  .feedback-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4rpx;
  }

  .user-name {
    margin-right: 0;
    margin-bottom: 4rpx;
  }

  .stats-badge {
    width: 48rpx;
    height: 48rpx;
    border-radius: 24rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .feedback-record-page {
    background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
  }

  .page-header {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
  }

  .feedback-card {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
  }

  .title-text {
    background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .feedback-content {
    background: rgba(64, 169, 255, 0.05);
    border-left-color: rgba(64, 169, 255, 0.3);
  }
}
</style>
