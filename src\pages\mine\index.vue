<template>
  <view class="mine-page">
    <!-- 头部背景区域 -->
    <view class="header-section">
      <view class="header-bg">
        <view class="header-content">
          <view class="setting-btn" @click="toPath('/src/pages/mine/children/setting-up/index')">
            <wd-icon name="setting" color="#fff" size="24" />
          </view>
        </view>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <view class="avatar-container">
          <image class="avatar" :src="userInfo?.path ?? '/static/ico.png'" mode="aspectFill" />
          <view class="avatar-ring"></view>
        </view>
        <view class="user-details">
          <text class="username">{{ userInfo?.name ?? '未登录' }}</text>
          <text class="user-subtitle">数据支持系统</text>
        </view>
      </view>

      <!-- 统计数据区域 -->
      <view class="stats-section">
        <view class="stats-container">
          <view class="stat-item completed" @click="toPath('/src/pages/mine/children/record/list?type=true')">
            <view class="stat-icon">
              <wd-icon name="check-circle" color="#52c41a" size="20" />
            </view>
            <view class="stat-content">
              <view class="stat-number">{{ statisticsData.countFinish }}</view>
              <view class="stat-label">已处理</view>
            </view>
            <view class="stat-progress">
              <view class="progress-bar completed-bar" :style="{ width: getProgressWidth(statisticsData.countFinish) }"></view>
            </view>
          </view>

          <view class="stat-divider"></view>

          <view class="stat-item pending" @click="toPath('/src/pages/mine/children/record/list?type=false')">
            <view class="stat-icon">
              <wd-icon name="clock" color="#ff7875" size="20" />
            </view>
            <view class="stat-content">
              <view class="stat-number">{{ statisticsData.countUndone }}</view>
              <view class="stat-label">待处理</view>
            </view>
            <view class="stat-progress">
              <view class="progress-bar pending-bar" :style="{ width: getProgressWidth(statisticsData.countUndone) }"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单区域 -->
    <view class="menu-section">
      <!-- 核心功能区域 -->
      <view class="menu-group">
        <view class="group-header">
          <view class="group-title">
            <wd-icon name="layers" color="#1890ff" size="18" />
            <text class="group-title-text">核心功能</text>
          </view>
          <view class="group-badge">{{ coreMenus.length }}</view>
        </view>

        <view class="core-menu-grid">
          <template v-for="item in coreMenus" :key="item.type">
            <view class="core-menu-item" @click="toPageClick(item)">
              <view class="core-icon" :class="getMenuIconClass(item)">
                <wd-icon :name="item.icon" color="#fff" size="24" />
                <view class="icon-glow" :class="getGlowClass(item)"></view>
              </view>
              <view class="core-title">{{ item.title }}</view>
              <view class="core-subtitle">{{ getShortSubtitle(item) }}</view>
              <view class="core-status" v-if="getMenuStatus(item)">
                <view class="status-dot" :class="getStatusClass(item)"></view>
                <text class="status-text">{{ getMenuStatus(item) }}</text>
              </view>
            </view>
          </template>
        </view>
      </view>

      <!-- 管理功能区域 -->
      <view class="menu-group">
        <view class="group-header">
          <view class="group-title">
            <wd-icon name="setting" color="#722ed1" size="18" />
            <text class="group-title-text">管理功能</text>
          </view>
          <view class="group-badge">{{ managementMenus.length }}</view>
        </view>

        <view class="management-menu-list">
          <template v-for="item in managementMenus" :key="item.type">
            <view class="management-item" @click="toPageClick(item)" :class="{ 'item-danger': item.type === 'exit' }">
              <view class="management-icon" :class="getMenuIconClass(item)">
                <wd-icon :name="item.icon" color="#fff" size="20" />
              </view>
              <view class="management-content">
                <view class="management-title">{{ item.title }}</view>
                <view class="management-subtitle">{{ getMenuSubtitle(item) }}</view>
              </view>
              <view class="management-extra">
                <view class="extra-badge" v-if="getMenuBadge(item)">{{ getMenuBadge(item) }}</view>
                <wd-icon name="arrow-right" color="#c0c4cc" size="16" />
              </view>
            </view>
          </template>
        </view>
      </view>

      <!-- 快捷操作区域 -->
      <view class="quick-actions">
        <view class="quick-title">快捷操作</view>
        <view class="quick-buttons">
          <view class="quick-btn" @click="toPath('/src/pages/mine/children/record/list?type=false')">
            <wd-icon name="clock" color="#ff7875" size="20" />
            <text>待处理任务</text>
            <view class="quick-badge" v-if="statisticsData.countUndone > 0">{{ statisticsData.countUndone }}</view>
          </view>
          <view class="quick-btn" @click="toPath('/src/pages/mine/children/feedback/feedback.record')">
            <wd-icon name="chat" color="#ff4d4f" size="20" />
            <text>待处理档案</text>
            <view class="quick-badge" v-if="pendingCount > 0">{{ pendingCount }}</view>
          </view>
          <view class="quick-btn" @click="toPath('/src/pages/mine/children/feedback/index')">
            <wd-icon name="chat" color="#13c2c2" size="20" />
            <text>意见反馈</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  <wd-message-box />
  <wd-toast />
</template>

<script setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import uniUtil from '/src/utils/uniUtil'
import { useMessage } from 'wot-design-uni'
import { MaintainApi } from '/src/services/model/maintain.js'
import { feedbackPendingCountApi } from '/src/services/model/feedback.issue.js'
import { useToast } from 'wot-design-uni'

const userInfo = ref(uniUtil.get('userInfo') ?? {})
const toast = useToast()

const statisticsData = ref({ countFinish: 0, countUndone: 0 })
const pendingCount = ref(0)
onShow(async () => {
  try {
    const [data, data1] = await Promise.all([MaintainApi.statistics(), feedbackPendingCountApi()])
    statisticsData.value = data.data
    pendingCount.value = data1.data.count
  } catch (error) {
    console.log(error)

    toast.error('获取数据失败')
  }
})

// 核心功能菜单
const coreMenus = ref([
  {
    type: 'gis',
    title: '日常维护',
    path: '/src/pages/mine/children/record/list?code=0',
    icon: 'layers',
    color: '#1890ff',
    priority: 'high'
  },
  {
    type: 'valve',
    title: '总阀总表',
    path: '/src/pages/mine/children/field-work/list',
    icon: 'view-module',
    color: '#52c41a',
    priority: 'high'
  },
  {
    type: 'archive',
    title: '小区档案',
    path: '/src/pages/mine/children/material/list',
    icon: 'folder',
    color: '#fa8c16',
    priority: 'medium'
  },
  {
    type: 'verify',
    title: '管材核查专项',
    path: '/src/pages/mine/children/verify-pipe/VerifyPipeMap',
    icon: 'search',
    color: '#722ed1',
    priority: 'medium'
  }
])

// 管理功能菜单
const managementMenus = ref([
  {
    type: 'feedback',
    title: '帮助与反馈',
    path: '/src/pages/mine/children/feedback/index',
    icon: 'chat',
    color: '#13c2c2'
  },
  {
    type: 'password',
    title: '修改密码',
    path: '/src/pages/mine/children/setting-up/children/change-password',
    icon: 'lock',
    color: '#eb2f96'
  },
  {
    type: 'exit',
    title: '退出登录',
    path: '/src/pages/login/index',
    icon: 'logout',
    color: '#ff4d4f'
  }
])

// 保持原有的navigations数组以兼容现有代码
const navigations = [...coreMenus.value, ...managementMenus.value]
const message = useMessage()

function toPageClick(val) {
  if (val.type === 'exit') {
    return message.confirm({ msg: '确定退出登录？', title: '退出登录' }).then(() => {
      uniUtil.clear()
      uniUtil.reLaunch(val.path)
    })
  }
  if (val.type === 'code') {
    const name = uniUtil.get('userInfo').name
    if (name !== '开发账号') return uniUtil.showToast('无权限')
  }
  if (val.path) {
    uniUtil.navigateTo(val.path)
  } else {
    uniUtil.showToast('开发中敬请期待')
  }
}

function toPath(val) {
  if (val) {
    uniUtil.navigateTo(val)
  } else {
    uniUtil.showToast('开发中敬请期待')
  }
}

// 获取进度条宽度
function getProgressWidth(count) {
  const total = statisticsData.value.countFinish + statisticsData.value.countUndone
  if (total === 0) return '0%'
  return Math.min((count / total) * 100, 100) + '%'
}

// 获取菜单图标样式类
function getMenuIconClass(item) {
  const baseClass = 'menu-icon-bg'
  switch (item.type) {
    case 'gis':
      return `${baseClass} icon-blue`
    case 'valve':
      return `${baseClass} icon-green`
    case 'archive':
      return `${baseClass} icon-orange`
    case 'verify':
      return `${baseClass} icon-purple`
    case 'feedback':
      return `${baseClass} icon-cyan`
    case 'password':
      return `${baseClass} icon-pink`
    case 'exit':
      return `${baseClass} icon-red`
    default:
      return `${baseClass} icon-gray`
  }
}

// 获取菜单副标题
function getMenuSubtitle(item) {
  switch (item.type) {
    case 'gis':
      return '地理信息系统维护'
    case 'valve':
      return '总阀总表管理'
    case 'archive':
      return '小区档案管理'
    case 'verify':
      return '管材核查专项工作'
    case 'feedback':
      return '问题反馈与帮助'
    case 'password':
      return '账户安全设置'
    case 'exit':
      return '安全退出系统'
    default:
      return ''
  }
}

// 获取简短副标题（用于核心功能卡片）
function getShortSubtitle(item) {
  switch (item.type) {
    case 'gis':
      return 'GIS系统'
    case 'valve':
      return '阀表管理'
    case 'archive':
      return '档案管理'
    case 'verify':
      return '核查专项'
    default:
      return ''
  }
}

// 获取菜单状态
function getMenuStatus(item) {
  switch (item.type) {
    case 'gis':
      return statisticsData.value.countUndone > 0 ? '有待处理' : '正常'
    case 'valve':
      return '正常'
    case 'archive':
      return pendingCount.value > 0 ? '有待处理' : '正常'
    case 'verify':
      return '进行中'
    default:
      return ''
  }
}

// 获取状态样式类
function getStatusClass(item) {
  switch (item.type) {
    case 'gis':
      return statisticsData.value.countUndone > 0 ? 'status-warning' : 'status-success'
    case 'archive':
      return pendingCount.value > 0 ? 'status-warning' : 'status-success'
    case 'verify':
      return 'status-processing'
    default:
      return 'status-success'
  }
}

// 获取发光效果样式类
function getGlowClass(item) {
  switch (item.type) {
    case 'gis':
      return 'glow-blue'
    case 'valve':
      return 'glow-green'
    case 'archive':
      return 'glow-orange'
    case 'verify':
      return 'glow-purple'
    default:
      return ''
  }
}

// 获取菜单徽章
function getMenuBadge(item) {
  switch (item.type) {
    case 'feedback':
      return 'NEW'
    case 'password':
      return ''
    case 'exit':
      return ''
    default:
      return ''
  }
}
</script>

<style lang="less" scoped>
.mine-page {
  min-height: 100vh;
  // background: linear-gradient(180deg, #f8faff 0%, #f0f2f5 100%);
}

/* 头部区域 */
.header-section {
  position: relative;
  height: 320rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image: url(/static/main_top_bg.png);
  background-size: cover;
  background-position: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  }
}

.header-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 60rpx 32rpx;
}

.setting-btn {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
  }
}

/* 用户信息卡片 */
.user-card {
  margin: -120rpx 32rpx 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  z-index: 3;
}

.user-info {
  padding: 48rpx 32rpx 32rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 6rpx solid #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.avatar-ring {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  border: 4rpx solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  background-clip: padding-box;
  z-index: -1;
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.user-subtitle {
  display: block;
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 统计数据区域 */
.stats-section {
  padding: 0 32rpx 32rpx;
}

.stats-container {
  display: flex;
  background: #f8faff;
  border-radius: 16rpx;
  padding: 24rpx;
  gap: 24rpx;
}

.stat-item {
  flex: 1;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.stat-icon {
  margin-bottom: 12rpx;
}

.stat-content {
  margin-bottom: 16rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8c8c8c;
}

.stat-progress {
  height: 6rpx;
  background: rgba(0, 0, 0, 0.06);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 3rpx;
  transition: width 0.6s ease;
}

.completed .stat-number {
  color: #52c41a;
}

.completed-bar {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
}

.pending .stat-number {
  color: #ff7875;
}

.pending-bar {
  background: linear-gradient(90deg, #ff7875 0%, #ff9c6e 100%);
}

.stat-divider {
  width: 2rpx;
  background: #e8e8e8;
  margin: 8rpx 0;
}

/* 功能菜单区域 */
.menu-section {
  margin: 0 32rpx 32rpx;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 菜单分组 */
.menu-group {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #f8faff 0%, #f0f2f5 100%);
  border-bottom: 2rpx solid #f0f0f0;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.group-title-text {
  font-size: 28rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.group-badge {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: #fff;
  font-size: 20rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}

/* 核心功能网格 */
.core-menu-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rpx;
  background: #f0f0f0;
}

.core-menu-item {
  background: #fff;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;

  &:active {
    background: #f8faff;
    transform: scale(0.95);
  }
}

/* 核心功能图标 */
.core-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.1;
  }
}

.icon-glow {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  border-radius: 22rpx;
  opacity: 0.3;
  z-index: -1;

  &.glow-blue {
    box-shadow: 0 0 20rpx rgba(24, 144, 255, 0.4);
  }

  &.glow-green {
    box-shadow: 0 0 20rpx rgba(82, 196, 26, 0.4);
  }

  &.glow-orange {
    box-shadow: 0 0 20rpx rgba(250, 140, 22, 0.4);
  }

  &.glow-purple {
    box-shadow: 0 0 20rpx rgba(114, 46, 209, 0.4);
  }
}

.core-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.core-subtitle {
  font-size: 22rpx;
  color: #8c8c8c;
  margin-bottom: 12rpx;
}

.core-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 20rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;

  &.status-success {
    background: #52c41a;
  }

  &.status-warning {
    background: #faad14;
  }

  &.status-processing {
    background: #1890ff;
  }
}

.status-text {
  color: #8c8c8c;
}

/* 管理功能列表 */
.management-menu-list {
  padding: 0;
}

.management-item {
  display: flex;
  align-items: center;
  padding: 28rpx 32rpx;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8faff;
    transform: translateX(8rpx);
  }

  &.item-danger:active {
    background: #fff2f0;
  }
}

.management-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.1;
  }
}

.management-content {
  flex: 1;
  margin-right: 16rpx;
}

.management-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.management-subtitle {
  font-size: 22rpx;
  color: #8c8c8c;
  line-height: 1.4;
}

.management-extra {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.extra-badge {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
  font-size: 18rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  text-transform: uppercase;
}

.menu-icon-bg {
  &.icon-blue {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  }

  &.icon-green {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  }

  &.icon-orange {
    background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
  }

  &.icon-purple {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  }

  &.icon-cyan {
    background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
  }

  &.icon-pink {
    background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
  }

  &.icon-red {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  }

  &.icon-gray {
    background: linear-gradient(135deg, #8c8c8c 0%, #bfbfbf 100%);
  }
}

.menu-content {
  flex: 1;
  margin-right: 16rpx;
}

.menu-title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.menu-subtitle {
  font-size: 22rpx;
  color: #8c8c8c;
  line-height: 1.4;
}

.menu-arrow {
  opacity: 0.6;
  transition: all 0.3s ease;
}

/* 快捷操作区域 */
.quick-actions {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  position: relative;
  overflow: hidden;
  margin-bottom: 36rpx;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    background-size: 200rpx 200rpx;
  }
}

.quick-title {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
  margin-bottom: 24rpx;
  position: relative;
  z-index: 2;
}

.quick-buttons {
  display: flex;
  gap: 16rpx;
  position: relative;
  z-index: 2;
}

.quick-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  position: relative;

  &:active {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(0.95);
  }

  text {
    color: #fff;
    font-size: 22rpx;
    font-weight: 500;
  }
}

.quick-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4d4f;
  color: #fff;
  font-size: 18rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .user-card {
    margin: -100rpx 24rpx 24rpx;
  }

  .menu-section {
    margin: 0 24rpx 24rpx;
  }

  .header-content {
    padding: 50rpx 24rpx;
  }

  .core-menu-grid {
    grid-template-columns: 1fr;
  }

  .quick-buttons {
    flex-direction: column;
    gap: 12rpx;
  }

  .quick-btn {
    flex-direction: row;
    justify-content: center;
    padding: 20rpx;

    text {
      margin-left: 12rpx;
    }
  }
}
</style>
